import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  noConfig: true,
  pluginInfo: {
    pluginCode: "16image",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "16 图床",
    pluginDesc: "[无需登录]多重备份，分布式数据库，oss冷备份，全格式支持，尊重隐私，允许撤回图片",
    pluginVersion: "1.0.5",
    pluginGroup: 'self'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: 'token',
          name:  'token',
          tooltip: '自动生成',
        },
        elementProperty: {
          placeholder: '自动生成',
          readonly: true
        },
      },
    ]
  }
});

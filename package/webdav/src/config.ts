import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  noConfig: false,
  pluginInfo: {
    pluginCode: "webdav",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "webdav",
    pluginDesc: "[注意要可以获取直链] WebDAV 是基于 Internet 的开放标准，支持通过 HTTP 和 HTTPS 连接编辑网站。",
    pluginVersion: "1.0.2",
    pluginGroup: 'self'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: '服务器地址',
          name:  'serviceUrl',
          rules: [
            {
              required: true,
              message: '服务器地址 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: '请输入您的 WebDAV 链接地址'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '用户名',
          name:  'username',
          rules: [
            {
              required: true,
              message: '用户名 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: '请输入您的 WebDAV 用户名'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '密码',
          name:  'password',
          rules: [
            {
              required: true,
              message: '密码 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: '请输入您的 WebDAV 密码',
          type: 'password'
        },
      },
      {
        type: 'radio-group',
        formItem: {
          label: '认证类型',
          name: 'authType',
          defaultValue: 'Auto',
          data: [
            {
              label: '自动',
              value: 'Auto'
            },
            {
              label: '摘要',
              value: 'Digest'
            },
            {
              label: '密码',
              value: 'Password'
            },
            {
              label: '令牌',
              value: 'Token'
            }
          ]
        },
        elementProperty: {
          placeholder: '自定义域名拼接会是「域名 + 存储路径」',
        },
      },
      {
        type: 'input',
        formItem: {
          label: '直链前缀',
          name:  'directLinkPrefix',
          tooltip: '会将文件路径拼接在后面',
          rules: [
            {
              required: true,
              message: '直链前缀 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: '会将文件路径拼接在后面'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '挂载路径',
          name:  'mountPath',
          tooltip: '挂载路径将优先拼接文件路径前',
        },
        elementProperty: {
          placeholder: '挂载路径默认是 / (根目录)'
        },
      },
      {
        type: 'input-format',
        formItem: {
          label: '文件路径',
          name: 'filePath',
        },
        elementProperty: {
          placeholder: '请输入文件路径',
          defaultValue: '{YY}-{M}/{filename}_{timestamp}.{suffix}'
        },
      },
    ]
  }
});

import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  pluginInfo: {
    pluginCode: "sm.ms",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "sm.ms",
    pluginDesc: "一个免费的图床服务，允许用户上传、存储和分享图片",
    pluginVersion: "1.0.3",
    pluginGroup: 'other'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: 'token',
          name:  'token',
          rules: [
            {
              required: true,
              message: 'token 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'token',
          type: 'password'
        },
      },
      {
        type: 'input-format',
        formItem: {
          type: 'file',
          label: '文件名',
          name: 'fileNameTemplate',
          rules: [
            {
              validator: async (value: any | undefined) => {
                if (!value) {
                  return true;
                }
                if (!value.toString().includes('/') && !value.toString().includes('\\') ) {
                  return true;
                }
                return { result: false, message: '文件名称不支持使用目录分割符', type: 'warning' };
              }
            }
          ]
        },
        elementProperty: {
          placeholder: '请输入文件名称',
          defaultValue: '{filename}.{suffix}'
        },
      },
    ]
  }
});

import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  pluginInfo: {
    pluginCode: "dogecloud",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "多吉云",
    pluginDesc: "云存储是多吉云提供的一种存储海量文件的分布式存储服务。你可以通过网络随时查看和存储你的文件。",
    pluginVersion: "1.0.1",
    pluginGroup: 'cloudVendor'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: 'bucket',
          name:  'bucket',
          rules: [
            {
              required: true,
              message: 'bucket 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'bucket',
        },
      },
      {
        type: 'input',
        formItem: {
          label: 'accessKey',
          name:  'accessKey',
          rules: [
            {
              required: true,
              message: 'accessKey 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'accessKey',
        },
      },
      {
        type: 'input',
        formItem: {
          label: 'secretKey',
          name:  'secretKey',
          rules: [
            {
              required: true,
              message: 'secretKey 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'secretKey',
          type: 'password'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '加速域名',
          name: 'domain',
          tooltip: '加速域名拼接会是「域名 + 存储路径」不会自动拼接存储桶名',
          rules: [
            {
              required: true,
              message: '加速域名是必填的, 可以填测试域名配置'
            },
            {
              validator: async (value: any | undefined) => {
                if (!value) {
                  return true;
                }
                if (value.toString().startsWith('http://') || value.toString().startsWith('https://')) {
                  return true;
                }
                return { result: false, message: '加速域名需要 http 和 https 开头', type: 'warning' };
              }
            }
          ]
        },
        elementProperty: {
          placeholder: '自定义域名拼接会是「域名 + 存储路径」',
        },
      },
      {
        type: 'input-format',
        formItem: {
          label: '文件路径',
          field: 'filePath',
        },
        elementProperty: {
          placeholder: '请输入文件路径',
          defaultValue: '{YY}-{M}/{filename}_{timestamp}.{suffix}'
        },
      },
    ]
  }
});

import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  pluginInfo: {
    pluginCode: "tencent-git-cloud",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "腾讯云原生仓库",
    pluginDesc: "腾讯云原生仓库作为图床的插件",
    pluginVersion: "1.0.0",
    pluginGroup: 'other'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: '组织',
          name: 'organization',
          rules: {
            required: true,
            message: '组织名称不能为空',
          }
        },
        elementProperty: {
          placeholder: '组织名称',
        },
      },
      {
        type: 'input',
        formItem: {
          label: '仓库',
          name: 'repository',
          rules: {
            required: true,
            message: '仓库不能为空',
          }
        },
        elementProperty: {
          placeholder: '仓库名称',
        },
      },
      {
        type: 'input',
        formItem: {
          label: 'token',
          name:  'token',
          rules: {
            required: true,
            message: 'token 不能为空',
          }
        },
        elementProperty: {
          placeholder: 'token',
          type: 'password'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '分支',
          name:  'branch',
          rules: {
          }
        },
        elementProperty: {
          placeholder: '分支名称自动获取第一个也可以自己填写',
        },
      },
      {
        type: 'input-format',
        formItem: {
          type: 'file',
          label: '文件名',
          name: 'fileNameTemplate',
          rules: {
            validator: async (value: any | undefined) => {
              if (!value) {
                return true;
              }
              if (!value.toString().includes('/') && !value.toString().includes('\\') ) {
                return true;
              }
              return { result: false, message: '文件名称不支持使用目录分割符', type: 'warning' };
            }
          }
        },
        elementProperty: {
          placeholder: '请输入文件名称',
          defaultValue: '{filename}.{suffix}'
        },
      },
    ]
  }
});

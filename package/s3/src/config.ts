import { createStoragePlugInConfig } from "@xiaou66/picture-plugin";
export default createStoragePlugInConfig({
  pluginInfo: {
    pluginCode: "s3",
    pluginLogo: "/assets/logo.png",
    pluginAuthor: "xiaou",
    pluginName: "aws-S3协议",
    pluginDesc: "S3 提供一个 RESTful API 以编程方式实现与该服务的交互。",
    pluginVersion: "1.0.8",
    pluginGroup: 'other'
  },
  uiConfig: {
    tips: '',
    forms: [
      {
        type: 'input',
        formItem: {
          label: 'endpoint',
          name:  'endpoint',
          rules: [
            {
              required: true,
              message: 'endpoint 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'endpoint',
        },
      },
      {
        type: 'input',
        formItem: {
          label: 'bucket',
          name:  'bucket',
          rules: [
            {
              required: true,
              message: 'bucket 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'bucket',
        },
      },
      {
        type: 'input',
        formItem: {
          label: 'accessKeyId',
          name:  'accessKeyId',
          rules: [
            {
              required: true,
              message: 'accessKeyId 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'accessKeyId',
        },
      },
      {
        type: 'input',
        formItem: {
          label: 'secretAccessKey',
          name:  'secretAccessKey',
          rules: [
            {
              required: true,
              message: 'secretAccessKey 不能为空',
            }
          ]
        },
        elementProperty: {
          placeholder: 'secretAccessKey',
          type: 'password'
        },
      },
      {
        type: 'input-format',
        formItem: {
          label: '文件路径',
          name: 'filePath',
        },
        elementProperty: {
          placeholder: '请输入文件路径',
          defaultValue: '{YY}-{M}/{filename}_{timestamp}.{suffix}'
        },
      },
      {
        type: 'input',
        formItem: {
          label: '自定义域名',
          field: 'domain',
          tooltip: '自定义域名拼接会是「域名 + 存储路径」不会自动拼接存储桶名',
          rules: [
            {
              validator: (value: any | undefined, callback: (error?: string) => void) => {
                if (!value) {
                  return;
                }
                if (value.toString().startsWith('http://') || value.toString().startsWith('https://')) {
                  return;
                }
                callback('自定义域名需要 http 和 https 开头');
              }
            }
          ]
        },
        elementProperty: {
          placeholder: '自定义域名拼接会是「域名 + 存储路径」',
        },
      },
      {
        type: 'radio-group',
        formItem: {
          label: '路径样式',
          name: 'pathStyle',
          defaultValue: 'virtual-hosted-style',
          data: [
            {
              tooltip: `https://<bucketName>.s3.amazonaws.com/<key>`,
              label: 'virtual-hosted-style',
              value: 'virtual-hosted-style'
            },
            {
              tooltip: `https://s3.amazonaws.com/<bucketName>/<key>`,
              label: 'path-style',
              value: 'path-style'
            }
          ]
        },
        elementProperty: {
          placeholder: '自定义域名拼接会是「域名 + 存储路径」',
        },
      },
    ]
  }
});

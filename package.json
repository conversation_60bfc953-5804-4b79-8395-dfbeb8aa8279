{"name": "storage-source-plugin", "version": "1.0.0", "description": "", "scripts": {"run:dev": "ts-node --require ./scripts/build/ts-node-env.ts --transpile-only scripts/dev/app.ts --env=dev", "build:dev": "ts-node --require ./scripts/build/ts-node-env.ts --transpile-only scripts/deploy/app.ts --env=dev", "build:test": "ts-node --require ./scripts/build/ts-node-env.ts --transpile-only scripts/deploy/app.ts --env=test", "build:prod": "ts-node --require ./scripts/build/ts-node-env.ts --transpile-only scripts/deploy/app.ts --env=prod"}, "dependencies": {"@aws-sdk/client-s3": "3.821.0", "@aws-sdk/lib-storage": "^3.821.0", "@inquirer/prompts": "^7.4.0", "@types/inquirer": "^9.0.7", "@types/node": "^20.12.5", "@xiaou66/picture-plugin": "^0.0.41", "ali-oss": "^6.22.0", "archiver": "^7.0.1", "aws-sdk": "^2.1692.0", "axios": "^1.8.4", "clipboardy": "^4.0.0", "copy-paste": "^1.5.3", "cos-js-sdk-v5": "^1.8.7", "create-hmac": "^1.1.7", "crypto-js": "^4.2.0", "dotenv": "^16.4.7", "form-data": "^4.0.2", "inquirer": "^12.5.0", "inquirer-search-checkbox": "^1.0.0", "nanoid": "^5.1.5", "node-fetch": "^3.3.2", "qiniu-js": "4.0.0-beta.6", "spark-md5": "^3.0.2", "webdav": "^5.8.0", "webdav-client": "^1.4.3"}, "devDependencies": {"@types/ali-oss": "^6.16.11", "@types/archiver": "^6.0.3", "@types/copy-paste": "^1.1.33", "@types/crypto-js": "^4.2.2", "@types/mime-types": "^2.1.4", "@vitejs/plugin-vue": "^5.2.3", "dotenv-cli": "^8.0.0", "rollup-plugin-copy": "^3.5.0", "ts-node": "^10.9.2", "typescript": "^5.8.2", "utools-api-types": "^6.1.0", "vite": "^6.2.3"}}